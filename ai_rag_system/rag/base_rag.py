import os
from abc import abstractmethod

from llama_index.core import VectorStoreIndex
from llama_index.core.indices.base import BaseIndex
from llama_index.core.node_parser import SentenceSplitter
from llama_index.core.storage.storage_context import DEFAULT_PERSIST_DIR


class RAG:
    @abstractmethod
    async def load_data(self):
        """加载数据"""

    async def create_index_local(self, persist_dir=DEFAULT_PERSIST_DIR) -> BaseIndex:
        """
        创建本地索引
        
        该函数通过加载数据、解析文档、创建向量存储索引并持久化到本地目录的完整流程来创建索引
        
        :param persist_dir: 本地持久化目录，用于存储索引数据，默认使用系统定义的持久化目录
        :return: BaseIndex 索引对象，可用于后续的查询操作
        """
        # 加载数据（数据连接器）
        data = await self.load_data()
        # 解析文档为节点
        node_parser = SentenceSplitter.from_defaults()
        nodes = node_parser.get_nodes_from_documents(data)
        # 创建向量存储索引
        index = VectorStoreIndex(nodes)
        # index = VectorStoreIndex.from_documents(data, show_progress=True)
        # 对向量数据库做持久化
        index.storage_context.persist(persist_dir=persist_dir)
        return index

    async def create_index_remote(self, collection_name: "default") -> BaseIndex:
        """
        创建远程索引
        
        该函数通过加载数据、解析文档、创建基于Milvus向量存储的索引来实现远程索引创建
        
        :param collection_name: Milvus集合名称，默认为"default"
        :return: BaseIndex 索引对象，可用于后续的查询操作
        """
        # 加载数据并解析为节点
        data = await self.load_data()
        node_parser = SentenceSplitter.from_defaults()
        nodes = node_parser.get_nodes_from_documents(data)
        # 创建Milvus向量存储实例
        vector_store = MilvusVectorStore(
            uri=os.getenv("MILVUS_URI","http://localhost:19530"),
            collection_name=collection_name,dim=512,overwrite=False
        )
        # 基于向量存储创建索引
        index = VectorStoreIndex(nodes)
        return index


    async def query(self, query: str):
        """pass"""