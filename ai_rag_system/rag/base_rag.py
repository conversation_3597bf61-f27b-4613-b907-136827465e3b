import os
from abc import abstractmethod

from llama_index.core import VectorStoreIndex
from llama_index.core.indices.base import BaseIndex
from llama_index.core.node_parser import SentenceSplitter
from llama_index.core.storage.storage_context import DEFAULT_PERSIST_DIR, StorageContext
from llama_index.vector_stores.milvus import MilvusVectorStore


class RAG:
    @abstractmethod
    async def load_data(self):
        """加载数据"""

    async def create_index_local(self, persist_dir=DEFAULT_PERSIST_DIR) -> BaseIndex:
        """
        创建本地索引
        
        该函数通过加载数据、解析文档、创建向量存储索引并持久化到本地目录的完整流程来创建索引
        
        :param persist_dir: 本地持久化目录，用于存储索引数据，默认使用系统定义的持久化目录
        :return: BaseIndex 索引对象，可用于后续的查询操作
        """
        # 加载数据（数据连接器）
        data = await self.load_data()
        # 解析文档为节点
        node_parser = SentenceSplitter.from_defaults()
        nodes = node_parser.get_nodes_from_documents(data)
        # 创建向量存储索引
        index = VectorStoreIndex(nodes)
        # index = VectorStoreIndex.from_documents(data, show_progress=True)
        # 对向量数据库做持久化
        index.storage_context.persist(persist_dir=persist_dir)
        return index

    async def create_index_remote(self, collection_name: str = "default") -> BaseIndex:
        """
        创建远程向量索引

        该方法通过连接到远程Milvus向量数据库来创建向量索引，实现数据的分布式存储和检索。
        与本地索引不同，远程索引将向量数据存储在外部的Milvus服务器中，支持更大规模的数据处理和多客户端访问。

        工作流程:
        1. 加载原始数据文档
        2. 使用SentenceSplitter将文档解析为文本节点
        3. 配置Milvus向量存储连接参数
        4. 创建基于Milvus的存储上下文
        5. 构建向量索引并关联到远程存储

        :param collection_name: Milvus集合名称，用于组织和管理向量数据，默认为"default"
        :type collection_name: str
        :return: 基于远程Milvus存储的向量索引对象，可用于后续的相似性搜索和查询操作
        :rtype: BaseIndex

        :raises ConnectionError: 当无法连接到Milvus服务器时抛出
        :raises ValueError: 当collection_name为空或无效时抛出

        注意事项:
        - 需要确保MILVUS_URI环境变量已正确配置
        - 向量维度固定为512，需要与嵌入模型的输出维度匹配
        - overwrite=False表示不会覆盖已存在的同名集合
        """

        # 步骤1: 加载原始数据文档（通过子类实现的load_data方法）
        data = await self.load_data()

        # 步骤2: 创建文档解析器，使用默认配置的句子分割器
        # SentenceSplitter会将长文档切分成适合向量化的文本块
        node_parser = SentenceSplitter.from_defaults()

        # 步骤3: 将文档切分为文本块并转换为节点列表
        # SentenceSplitter首先将长文档切分成多个文本块，然后将每个文本块封装为Node对象
        # 每个节点包含文本内容、元数据以及与原文档的关联信息
        nodes = node_parser.get_nodes_from_documents(data)

        # 步骤4: 配置Milvus向量存储实例
        vector_store = MilvusVectorStore(
            uri=os.getenv("MILVUS_URI"),              # Milvus服务器连接URI，从环境变量获取
            collection_name=collection_name,          # 指定集合名称，用于数据隔离和组织
            dim=512,                                  # 向量维度，必须与嵌入模型输出维度一致
            overwrite=False                           # 不覆盖已存在的集合，保护现有数据
        )

        # 步骤5: 创建存储上下文，将向量存储集成到LlamaIndex框架中
        storage_context = StorageContext.from_defaults(vector_store=vector_store)

        # 步骤6: 构建向量索引，将文本节点转换为向量并存储到Milvus
        # 这个过程包括: 文本嵌入 -> 向量存储 -> 索引构建
        index = VectorStoreIndex(nodes, storage_context=storage_context)

        return index


    async def query(self, query: str):
        """pass"""